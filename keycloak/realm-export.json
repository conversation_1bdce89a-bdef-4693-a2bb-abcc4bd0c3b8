{"id": "payroll", "realm": "payroll", "displayName": "Payroll MCP Realm", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Payroll MCP</span></div>", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "defaultRoles": ["offline_access", "uma_authorization", "default-roles-payroll"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "test-user-id", "username": "testuser", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "testpass123", "temporary": false}], "realmRoles": ["default-roles-payroll"], "clientRoles": {"payroll-mcp-client": ["payroll-user"]}}], "clients": [{"id": "payroll-mcp-client-id", "clientId": "payroll-mcp-client", "name": "Payroll MCP Client", "description": "Model Context Protocol client for payroll data access", "rootUrl": "http://localhost:3000", "adminUrl": "http://localhost:3000", "baseUrl": "/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "payroll-mcp-client-secret-change-in-production", "redirectUris": ["http://localhost:3000/auth/callback", "https://mcp.example.com/auth/callback"], "webOrigins": ["http://localhost:3000", "https://mcp.example.com"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "audience-mapper", "name": "audience-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"included.client.audience": "payroll-mcp-client", "id.token.claim": "false", "access.token.claim": "true"}}, {"id": "resource-mapper", "name": "resource-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-hardcoded-claim-mapper", "consentRequired": false, "config": {"claim.value": "https://mcp.example.com", "claim.name": "resource", "id.token.claim": "false", "access.token.claim": "true", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "payroll-scope", "name": "payroll", "description": "Payroll data access scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "Access to payroll data"}, "protocolMappers": [{"id": "payroll-audience-mapper", "name": "payroll-audience-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"included.custom.audience": "https://mcp.example.com", "id.token.claim": "false", "access.token.claim": "true"}}]}], "roles": {"realm": [{"id": "payroll-admin", "name": "payroll-admin", "description": "Payroll administrator role", "composite": false, "clientRole": false, "containerId": "payroll"}, {"id": "payroll-user", "name": "payroll-user", "description": "Payroll user role", "composite": false, "clientRole": false, "containerId": "payroll"}], "client": {"payroll-mcp-client": [{"id": "payroll-access", "name": "payroll-access", "description": "Access to payroll MCP tools", "composite": false, "clientRole": true, "containerId": "payroll-mcp-client-id"}]}}}