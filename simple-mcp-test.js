#!/usr/bin/env node

/**
 * Simple MCP Server Test
 * 
 * This is a simplified test that validates the basic MCP functionality
 * by establishing an SSE connection and sending a few test messages.
 */

import EventSource from 'eventsource';
import fetch from 'node-fetch';

class SimpleMCPTest {
  constructor(serverUrl = 'http://localhost:3001/mcp') {
    this.serverUrl = serverUrl;
    this.sessionId = null;
    this.eventSource = null;
    this.messageId = 1;
    this.responses = new Map();
  }

  async run() {
    console.log('🧪 Simple MCP Server Test');
    console.log('=========================');
    console.log('Target:', this.serverUrl);
    console.log('');

    try {
      // Step 1: Establish SSE connection
      await this.establishSSEConnection();
      
      // Wait a moment for connection to stabilize
      await this.sleep(1000);
      
      // Step 2: Send initialize request
      await this.testInitialize();
      
      // Step 3: Send tools/list request
      await this.testToolsList();
      
      console.log('\n✅ All tests passed! Your MCP server is working correctly.');
      
    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    } finally {
      if (this.eventSource) {
        this.eventSource.close();
      }
    }
  }

  async establishSSEConnection() {
    console.log('🔌 Establishing SSE connection...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('SSE connection timeout'));
      }, 10000);

      this.eventSource = new EventSource(this.serverUrl);

      this.eventSource.onopen = () => {
        clearTimeout(timeout);
        console.log('✅ SSE connection established');
        resolve();
      };

      this.eventSource.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error('SSE connection failed'));
      };

      // Listen for endpoint event
      this.eventSource.addEventListener('endpoint', (event) => {
        console.log('📡 Endpoint event:', event.data);
        if (event.data.includes('sessionId=')) {
          this.sessionId = event.data.split('sessionId=')[1];
          console.log('🆔 Session ID:', this.sessionId);
        }
      });

      // Listen for message events
      this.eventSource.addEventListener('message', (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('📨 Received:', JSON.stringify(message, null, 2));
          
          if (message.id && this.responses.has(message.id)) {
            const { resolve: resolveResponse } = this.responses.get(message.id);
            this.responses.delete(message.id);
            resolveResponse(message);
          }
        } catch (error) {
          console.error('❌ Failed to parse message:', error);
        }
      });
    });
  }

  async sendMessage(method, params = {}) {
    const messageId = this.messageId++;
    const message = {
      jsonrpc: '2.0',
      id: messageId,
      method,
      params
    };

    console.log(`📤 Sending ${method}:`, JSON.stringify(message, null, 2));

    // Set up response promise
    const responsePromise = new Promise((resolve, reject) => {
      this.responses.set(messageId, { resolve, reject });
      
      setTimeout(() => {
        if (this.responses.has(messageId)) {
          this.responses.delete(messageId);
          reject(new Error(`Timeout waiting for response to ${method}`));
        }
      }, 15000);
    });

    // Send POST request
    const headers = { 'Content-Type': 'application/json' };
    if (this.sessionId) {
      headers['Mcp-Session-Id'] = this.sessionId;
    }

    const response = await fetch(this.serverUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(message)
    });

    if (!response.ok) {
      this.responses.delete(messageId);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseText = await response.text();
    console.log(`📥 POST response: ${response.status} - ${responseText}`);

    // Wait for SSE response
    return responsePromise;
  }

  async testInitialize() {
    console.log('\n🚀 Testing initialize...');
    
    const response = await this.sendMessage('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {
        roots: { listChanged: false },
        sampling: {}
      },
      clientInfo: {
        name: 'simple-mcp-test',
        version: '1.0.0'
      }
    });

    if (response.error) {
      throw new Error(`Initialize failed: ${response.error.message}`);
    }

    console.log('✅ Initialize successful');
    console.log('   Server:', response.result?.serverInfo?.name || 'Unknown');
    console.log('   Version:', response.result?.serverInfo?.version || 'Unknown');
    console.log('   Protocol:', response.result?.protocolVersion || 'Unknown');

    // Send initialized notification
    await this.sendInitializedNotification();
  }

  async sendInitializedNotification() {
    console.log('📢 Sending initialized notification...');
    
    const headers = { 'Content-Type': 'application/json' };
    if (this.sessionId) {
      headers['Mcp-Session-Id'] = this.sessionId;
    }

    const notification = {
      jsonrpc: '2.0',
      method: 'notifications/initialized'
    };

    const response = await fetch(this.serverUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(notification)
    });

    if (response.ok) {
      console.log('✅ Initialized notification sent');
    } else {
      console.warn('⚠️  Initialized notification failed:', response.status);
    }
  }

  async testToolsList() {
    console.log('\n🛠️  Testing tools/list...');
    
    const response = await this.sendMessage('tools/list');

    if (response.error) {
      throw new Error(`Tools list failed: ${response.error.message}`);
    }

    console.log('✅ Tools list successful');
    const tools = response.result?.tools || [];
    console.log(`   Found ${tools.length} tool(s):`);
    
    tools.forEach(tool => {
      console.log(`   - ${tool.name}: ${tool.description || 'No description'}`);
    });

    // Check for payroll tool
    const payrollTool = tools.find(tool => tool.name === 'payroll');
    if (payrollTool) {
      console.log('✅ Payroll tool found and registered correctly');
    } else {
      console.warn('⚠️  Payroll tool not found');
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
const test = new SimpleMCPTest();
test.run().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
