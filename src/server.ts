import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';

import { PayrollApiService } from './services/payroll-api.js';
import { PayrollTool } from './tools/payroll.js';
import { Logger, defaultLogger } from './utils/logger.js';
import { MCP_ERROR_CODES } from './types/payroll.js';

// Environment configuration
const config = {
  port: parseInt(process.env['PORT'] || '3000'),
  externalServiceAccessToken: process.env['EXTERNAL_SERVICE_ACCESS_TOKEN'] || '',
  externalApi: {
    baseUrl: process.env['EXTERNAL_API_URL'] || '',
    timeout: parseInt(process.env['API_TIMEOUT'] || '30000'),
    retries: parseInt(process.env['API_RETRIES'] || '3'),
    retryDelay: parseInt(process.env['API_RETRY_DELAY'] || '1000')
  },
  security: {
    enableDnsRebindingProtection: process.env['ENABLE_DNS_REBINDING_PROTECTION'] === 'true',
    allowedHosts: (process.env['ALLOWED_HOSTS'] || '127.0.0.1,localhost').split(','),
    allowedOrigins: (process.env['ALLOWED_ORIGINS'] || '').split(',').filter(Boolean),
    rateLimitWindowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000'), // 15 minutes
    rateLimitMax: parseInt(process.env['RATE_LIMIT_MAX'] || '100') // requests per window
  }
};

// Validate required configuration
function validateConfig(): void {
  const required = [
    'EXTERNAL_SERVICE_ACCESS_TOKEN',
    'EXTERNAL_API_URL'
  ];

  const missing = required.filter(key => !process.env[key]);
  if (missing.length > 0) {
    defaultLogger.error('Missing required environment variables', { missing });
    process.exit(1);
  }
}

class PayrollMcpServer {
  private app: express.Application;
  private payrollApiService: PayrollApiService;
  private payrollTool: PayrollTool;
  private transports: Map<string, SSEServerTransport> = new Map();
  private logger: Logger;

  constructor() {
    this.logger = defaultLogger;
    this.app = express();

    // Initialize services
    this.payrollApiService = new PayrollApiService(config.externalApi, this.logger);
    this.payrollTool = new PayrollTool(this.payrollApiService, this.logger);
  }

  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Payroll MCP Server', { version: '1.0.0' });

      // Validate configuration
      validateConfig();

      // Setup Express middleware
      this.setupMiddleware();

      // Setup MCP routes
      this.setupMcpRoutes();

      // Setup error handling
      this.setupErrorHandling();

      this.logger.info('Payroll MCP Server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize server', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Allow MCP protocol
      crossOriginEmbedderPolicy: false
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.security.rateLimitWindowMs,
      max: config.security.rateLimitMax,
      message: {
        jsonrpc: '2.0',
        error: {
          code: MCP_ERROR_CODES.INVALID_REQUEST,
          message: 'Too many requests'
        },
        id: null
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use(limiter);

    // CORS configuration
    this.app.use(cors({
      origin: config.security.allowedOrigins.length > 0 ? config.security.allowedOrigins : true,
      credentials: false,
      exposedHeaders: ['Content-Type'],
      allowedHeaders: ['Content-Type', 'Origin']
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
  }

  private setupMcpRoutes(): void {
    // MCP endpoint - handles both GET (SSE) and POST (messages) requests
    this.app.all('/mcp', async (req, res): Promise<void> => {
      try {
        if (req.method === 'GET') {
          // Handle SSE connection establishment
          this.logger.info('Establishing SSE connection');

          const transport = new SSEServerTransport('/mcp', res, {
            enableDnsRebindingProtection: config.security.enableDnsRebindingProtection,
            allowedHosts: config.security.allowedHosts,
            // allowedOrigins: config.security.allowedOrigins
          });

          // Store transport for cleanup
          this.transports.set(transport.sessionId, transport);

          // Clean up transport when closed
          transport.onclose = () => {
            this.transports.delete(transport.sessionId);
            this.logger.info('SSE transport closed', { sessionId: transport.sessionId });
          };

          // Create and connect MCP server
          const mcpServer = await this.createMcpServer();
          await mcpServer.connect(transport);

          this.logger.info('SSE transport established', { sessionId: transport.sessionId });
        } else if (req.method === 'POST') {
          // Handle incoming messages
          // Try to find the right transport by checking all active transports
          let handled = false;

          for (const [sessionId, transport] of this.transports.entries()) {
            try {
              await transport.handlePostMessage(req, res, req.body);
              handled = true;
              this.logger.debug('Message handled by transport', { sessionId });
              break;
            } catch (error) {
              // This transport couldn't handle the message, try the next one
              this.logger.debug('Transport failed to handle message', {
                sessionId,
                error: error instanceof Error ? error.message : String(error)
              });
            }
          }

          if (!handled) {
            this.logger.error('No transport could handle the POST message');
            res.status(400).json({
              jsonrpc: '2.0',
              error: {
                code: MCP_ERROR_CODES.INVALID_REQUEST,
                message: 'No active session found'
              },
              id: null
            });
          }
        } else {
          res.status(405).json({
            jsonrpc: '2.0',
            error: {
              code: MCP_ERROR_CODES.METHOD_NOT_FOUND,
              message: 'Method not allowed'
            },
            id: null
          });
        }
      } catch (error) {
        this.logger.error('MCP request error', {
          error: error instanceof Error ? error.message : String(error),
          method: req.method
        });

        if (!res.headersSent) {
          res.status(500).json({
            jsonrpc: '2.0',
            error: {
              code: MCP_ERROR_CODES.INTERNAL_ERROR,
              message: 'Internal server error'
            },
            id: null
          });
        }
      }
    });

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        services: {
          external_api: 'configured'
        }
      });
    });

  }

  private async createMcpServer(): Promise<McpServer> {
    const mcpServer = new McpServer({
      name: 'payroll-mcp-server',
      version: '1.0.0'
    });

    // Register the payroll tool
    mcpServer.registerTool(
      'payroll',
      {
        title: 'Payroll Data Tool',
        description: 'Retrieve payroll data for owners, heritors, and their associated contracts with comprehensive filtering options',
        inputSchema: PayrollTool.getZodSchema()
      },
      async (params: any) => {
        // Use the configured external service access token
        const accessToken = config.externalServiceAccessToken;
        if (!accessToken) {
          throw new Error('External service access token not configured');
        }

        return await this.payrollTool.execute(params as any, accessToken);
      }
    );

    this.logger.info('MCP server created with payroll tool');
    return mcpServer;
  }

  private setupErrorHandling(): void {

    // Generic error handler
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      if (res.headersSent) {
        return next(error);
      }

      this.logger.error('Unhandled error', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method
      });

      res.status(500).json({
        jsonrpc: '2.0',
        error: {
          code: MCP_ERROR_CODES.INTERNAL_ERROR,
          message: 'Internal server error'
        },
        id: null
      });
    });

    // 404 handler
    this.app.use((req, res) => {
      this.logger.warn('Route not found', { path: req.path, method: req.method });

      res.status(404).json({
        jsonrpc: '2.0',
        error: {
          code: MCP_ERROR_CODES.METHOD_NOT_FOUND,
          message: 'Endpoint not found'
        },
        id: null
      });
    });
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const server = this.app.listen(config.port, () => {
          this.logger.info('Payroll MCP Server started', {
            port: config.port,
            environment: process.env['NODE_ENV'] || 'development'
          });
          resolve();
        });

        server.on('error', (error) => {
          this.logger.error('Server error', { error: error.message });
          reject(error);
        });

        // Graceful shutdown
        process.on('SIGTERM', () => {
          this.logger.info('SIGTERM received, shutting down gracefully');
          server.close(() => {
            this.logger.info('Server closed');
            process.exit(0);
          });
        });

        process.on('SIGINT', () => {
          this.logger.info('SIGINT received, shutting down gracefully');
          server.close(() => {
            this.logger.info('Server closed');
            process.exit(0);
          });
        });

      } catch (error) {
        reject(error);
      }
    });
  }
}

// Start the server
async function main(): Promise<void> {
  try {
    const server = new PayrollMcpServer();
    await server.initialize();
    await server.start();
  } catch (error) {
    defaultLogger.error('Failed to start server', {
      error: error instanceof Error ? error.message : String(error)
    });
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  defaultLogger.error('Uncaught exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  defaultLogger.error('Unhandled rejection', { reason, promise });
  process.exit(1);
});

// Start the application
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    defaultLogger.error('Application startup failed', { error: error.message });
    process.exit(1);
  });
}
