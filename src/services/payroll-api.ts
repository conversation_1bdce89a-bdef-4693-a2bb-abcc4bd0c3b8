import axios, { AxiosError } from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import { Logger } from '../utils/logger.js';
import type {
  PayrollParameters,
  JsonRpcRequest,
  JsonRpcResponse,
  PayrollApiResponse
} from '../types/payroll.js';

export interface PayrollApiConfig {
  baseUrl: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export class PayrollApiService {
  private client: AxiosInstance;
  private logger: Logger;
  private config: PayrollApiConfig;
  private requestIdCounter = 1;

  constructor(config: PayrollApiConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;

    // Create axios instance with default configuration
    this.client = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'PayrollMCP/1.0.0'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        const startTime = Date.now();
        (config as any).metadata = { startTime };
        
        this.logger.api('Outgoing request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          hasAuth: !!config.headers?.Authorization
        });
        
        return config;
      },
      (error) => {
        this.logger.error('Request interceptor error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        const duration = Date.now() - ((response.config as any).metadata?.startTime || 0);
        
        this.logger.api('Response received', {
          status: response.status,
          duration,
          url: response.config.url,
          dataSize: JSON.stringify(response.data).length
        });
        
        return response;
      },
      (error: AxiosError) => {
        const duration = Date.now() - ((error.config as any)?.metadata?.startTime || 0);
        
        this.logger.externalApiCall(
          error.config?.url || 'unknown',
          error.config?.method?.toUpperCase() || 'unknown',
          error.response?.status,
          duration,
          error
        );
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * Call the payroll grid API with the specified parameters
   */
  async getPayrollData(
    parameters: PayrollParameters,
    accessToken: string,
    page: number = 1,
    rows: number = 30,
    sort: string = 'owner_names',
    order: 'asc' | 'desc' = 'asc'
  ): Promise<PayrollApiResponse> {
    const requestId = this.requestIdCounter++;
    const startTime = Date.now();

    try {
      this.logger.api('Calling payroll API', {
        requestId,
        parameters: this.sanitizeParameters(parameters),
        page,
        rows,
        sort,
        order
      });

      // Prepare JSON-RPC request
      const jsonRpcRequest: JsonRpcRequest = {
        method: 'read',
        params: [parameters, page, rows, sort, order],
        id: requestId,
        jsonrpc: '2.0'
      };

      // Make the API call
      const response = await this.makeRequest(jsonRpcRequest, accessToken);
      
      // Validate response structure
      this.validateResponse(response.data);

      const duration = Date.now() - startTime;
      this.logger.api('Payroll API call successful', {
        requestId,
        duration,
        totalRecords: response.data.result?.total || 0,
        recordsReturned: response.data.result?.rows?.length || 0
      });

      return response.data.result as PayrollApiResponse;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Payroll API call failed', {
        requestId,
        duration,
        error: error instanceof Error ? error.message : String(error),
        parameters: this.sanitizeParameters(parameters)
      });

      throw this.handleApiError(error, requestId);
    }
  }

  /**
   * Make HTTP request to the external API with retry logic
   */
  private async makeRequest(
    jsonRpcRequest: JsonRpcRequest,
    accessToken: string,
    attempt: number = 1
  ): Promise<AxiosResponse<JsonRpcResponse<PayrollApiResponse>>> {
    try {
      const response = await this.client.post<JsonRpcResponse<PayrollApiResponse>>(
        '/index.php?payroll-rpc=payroll-grid',
        jsonRpcRequest,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'X-Request-ID': String(jsonRpcRequest.id)
          }
        }
      );

      return response;

    } catch (error) {
      const maxRetries = this.config.retries || 3;
      const retryDelay = this.config.retryDelay || 1000;

      // Check if we should retry
      if (attempt < maxRetries && this.shouldRetry(error)) {
        this.logger.warn('Retrying API request', {
          attempt,
          maxRetries,
          requestId: jsonRpcRequest.id,
          error: error instanceof Error ? error.message : String(error)
        });

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        
        return this.makeRequest(jsonRpcRequest, accessToken, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * Determine if an error should trigger a retry
   */
  private shouldRetry(error: unknown): boolean {
    if (axios.isAxiosError(error)) {
      // Retry on network errors or 5xx server errors
      if (!error.response) {
        return true; // Network error
      }
      
      const status = error.response.status;
      return status >= 500 && status < 600; // Server errors
    }
    
    return false;
  }

  /**
   * Validate the JSON-RPC response structure
   */
  private validateResponse(response: JsonRpcResponse<PayrollApiResponse>): void {
    if (!response) {
      throw new Error('Empty response received');
    }

    if (response.jsonrpc !== '2.0') {
      throw new Error('Invalid JSON-RPC version');
    }

    if (response.error) {
      throw new Error(`API Error: ${response.error.message} (Code: ${response.error.code})`);
    }

    if (!response.result) {
      throw new Error('No result in response');
    }

    const result = response.result;
    if (!Array.isArray(result.rows)) {
      throw new Error('Invalid response structure: rows must be an array');
    }

    if (typeof result.total !== 'number') {
      throw new Error('Invalid response structure: total must be a number');
    }

    if (!Array.isArray(result.footer)) {
      throw new Error('Invalid response structure: footer must be an array');
    }
  }

  /**
   * Handle and transform API errors
   */
  private handleApiError(error: unknown, _requestId: number): Error {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const data = error.response?.data;

      // Handle specific HTTP status codes
      switch (status) {
        case 401:
          return new Error(`Authentication failed: ${data?.message || 'Invalid or expired token'}`);
        case 403:
          return new Error(`Access forbidden: ${data?.message || 'Insufficient permissions'}`);
        case 404:
          return new Error(`API endpoint not found: ${error.config?.url}`);
        case 429:
          return new Error(`Rate limit exceeded: ${data?.message || 'Too many requests'}`);
        case 500:
        case 502:
        case 503:
        case 504:
          return new Error(`Server error: ${data?.message || 'External API unavailable'}`);
        default:
          if (status && status >= 400) {
            return new Error(`HTTP ${status}: ${data?.message || error.message}`);
          }
      }

      // Network or timeout errors
      if (error.code === 'ECONNABORTED') {
        return new Error('Request timeout: External API did not respond in time');
      }
      
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        return new Error('Network error: Cannot connect to external API');
      }
    }

    // Generic error handling
    const message = error instanceof Error ? error.message : String(error);
    return new Error(`External API error: ${message}`);
  }

  /**
   * Sanitize parameters for logging (remove sensitive data)
   */
  private sanitizeParameters(parameters: PayrollParameters): Partial<PayrollParameters> {
    const sanitized = { ...parameters };
    
    // Remove or mask sensitive fields if any
    // For now, all fields seem safe to log
    
    return sanitized;
  }

  /**
   * Test the API connection
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      this.logger.api('Testing API connection');
      
      // Make a simple request to test connectivity
      const testParams: PayrollParameters = {
        type: 'owners',
        farming_year: 1
      };

      await this.getPayrollData(testParams, accessToken, 1, 1);
      
      this.logger.api('API connection test successful');
      return true;
      
    } catch (error) {
      this.logger.error('API connection test failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Get API health status
   */
  async getHealthStatus(): Promise<{ status: 'healthy' | 'unhealthy'; details?: string }> {
    try {
      // Simple health check without authentication
      const response = await this.client.get('/health', { timeout: 5000 });
      
      if (response.status === 200) {
        return { status: 'healthy' };
      } else {
        return { status: 'unhealthy', details: `HTTP ${response.status}` };
      }
    } catch (error) {
      return { 
        status: 'unhealthy', 
        details: error instanceof Error ? error.message : String(error) 
      };
    }
  }
}
