import winston from 'winston';
import { randomUUID } from 'crypto';

// Create a logger instance with structured logging
const logger = winston.createLogger({
  level: process.env['LOG_LEVEL'] || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level,
        message,
        ...meta
      });
    })
  ),
  defaultMeta: {
    service: 'payroll-mcp-server',
    version: process.env['npm_package_version'] || '1.0.0'
  },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
        winston.format.printf(({ timestamp, level, message, requestId, ...meta }) => {
          const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
          const reqId = requestId ? ` [${requestId}]` : '';
          return `${timestamp} ${level}:${reqId} ${message}${metaStr}`;
        })
      )
    }),
    
    // File transport for production logs
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
  
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// Create logs directory if it doesn't exist
import { mkdirSync } from 'fs';
try {
  mkdirSync('logs', { recursive: true });
} catch (error) {
  // Directory might already exist
}

// Request ID context for tracing
// const requestContexts = new Map<string, string>();

export class Logger {
  private requestId?: string;

  constructor(requestId?: string) {
    this.requestId = requestId || '';
  }

  static createRequestLogger(): Logger {
    const requestId = randomUUID();
    return new Logger(requestId);
  }

  static fromRequestId(requestId: string): Logger {
    return new Logger(requestId);
  }

  private log(level: string, message: string, meta: Record<string, unknown> = {}): void {
    const logMeta = {
      ...meta,
      ...(this.requestId && { requestId: this.requestId })
    };

    logger.log(level, message, logMeta);
  }

  error(message: string, meta: Record<string, unknown> = {}): void {
    this.log('error', message, meta);
  }

  warn(message: string, meta: Record<string, unknown> = {}): void {
    this.log('warn', message, meta);
  }

  info(message: string, meta: Record<string, unknown> = {}): void {
    this.log('info', message, meta);
  }

  debug(message: string, meta: Record<string, unknown> = {}): void {
    this.log('debug', message, meta);
  }

  // Specialized logging methods for different components
  oidc(message: string, meta: Record<string, unknown> = {}): void {
    this.info(message, { component: 'oidc', ...meta });
  }

  mcp(message: string, meta: Record<string, unknown> = {}): void {
    this.info(message, { component: 'mcp', ...meta });
  }

  api(message: string, meta: Record<string, unknown> = {}): void {
    this.info(message, { component: 'external-api', ...meta });
  }

  tool(message: string, meta: Record<string, unknown> = {}): void {
    this.info(message, { component: 'tool', ...meta });
  }

  // Log HTTP requests
  httpRequest(method: string, url: string, statusCode?: number, duration?: number): void {
    this.info('HTTP Request', {
      component: 'http',
      method,
      url,
      statusCode,
      duration
    });
  }

  // Log authentication events
  authEvent(event: string, userId?: string, success: boolean = true): void {
    this.info(`Auth: ${event}`, {
      component: 'auth',
      userId,
      success,
      event
    });
  }

  // Log tool executions
  toolExecution(toolName: string, parameters: Record<string, unknown>, success: boolean, duration?: number): void {
    this.info(`Tool execution: ${toolName}`, {
      component: 'tool',
      toolName,
      parameters,
      success,
      duration
    });
  }

  // Log external API calls
  externalApiCall(url: string, method: string, statusCode?: number, duration?: number, error?: Error): void {
    if (error) {
      this.error('External API call failed', {
        component: 'external-api',
        url,
        method,
        statusCode,
        duration,
        error: error.message,
        stack: error.stack
      });
    } else {
      this.info('External API call', {
        component: 'external-api',
        url,
        method,
        statusCode,
        duration
      });
    }
  }

  // Log MCP protocol events
  mcpEvent(event: string, sessionId?: string, meta: Record<string, unknown> = {}): void {
    this.info(`MCP: ${event}`, {
      component: 'mcp',
      event,
      sessionId,
      ...meta
    });
  }

  getRequestId(): string | undefined {
    return this.requestId;
  }
}

// Default logger instance
export const defaultLogger = new Logger();

// Export the winston logger for advanced usage
export { logger as winstonLogger };

// Utility function to create a child logger with additional context
export function createChildLogger(context: Record<string, unknown>): Logger {
  const childLogger = new Logger();
  // Override the log method to include context
  const originalLog = (childLogger as any).log;
  (childLogger as any).log = function(level: string, message: string, meta: Record<string, unknown> = {}) {
    return originalLog.call(this, level, message, { ...context, ...meta });
  };
  return childLogger;
}
