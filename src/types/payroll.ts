import { z } from 'zod';

// Payroll API Parameters Schema based on payroll-grid-api-parameters.md
export const PayrollParametersSchema = z.object({
  // Core Parameters (Required)
  type: z.enum(['owners', 'sums', 'payroll_by_owner']).describe('Operation mode and type of data returned'),
  farming_year: z.number().int().positive().describe('Farming year ID for payroll calculations'),

  // Date Range Parameters
  payroll_from_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional().describe('Start date for payroll period (YYYY-MM-DD)'),
  payroll_to_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional().describe('End date for payroll period (YYYY-MM-DD)'),

  // Location and Administrative Filters
  payroll_ekate: z.array(z.string()).optional().describe('EKATE administrative territorial codes'),
  payroll_farming: z.array(z.union([z.string(), z.number()])).optional().describe('Farming operations/entities IDs'),

  // Owner Type and Identification Filters
  owner_type: z.string().optional().describe('Owner type: "0" for companies, "1" for individuals, "0,1" for both'),
  owner_names: z.string().optional().describe('Text search filter for owner names (individuals only)'),
  egn: z.string().optional().describe('Individual identification number (EGN)'),
  eik: z.string().optional().describe('Company identification number (EIK)'),
  company_name: z.string().optional().describe('Text search filter for company names'),

  // Advanced Owner Filters
  owner_egns: z.array(z.string()).optional().describe('Multiple individual identification numbers'),
  company_eiks: z.array(z.string()).optional().describe('Multiple company identification numbers'),

  // Representative Filters
  rep_names: z.string().optional().describe('Text search filter for representative names'),
  rep_egn: z.string().optional().describe('Representative identification number'),
  rep_rent_place: z.string().optional().describe('Representative rent place/location'),

  // Location Filters
  rent_place: z.string().optional().describe('Owner rent place/location'),

  // Heritor Filters
  heritor_names: z.string().optional().describe('Text search filter for heritor names'),
  heritor_egn: z.string().optional().describe('Heritor identification number'),
});

export type PayrollParameters = z.infer<typeof PayrollParametersSchema>;

// JSON-RPC Request/Response Types
export interface JsonRpcRequest {
  method: string;
  params: [PayrollParameters, number, number, string, string]; // [data, page, rows, sort, order]
  id: number;
  jsonrpc: '2.0';
}

export interface JsonRpcResponse<T = unknown> {
  jsonrpc: '2.0';
  id: number;
  result?: T;
  error?: {
    code: number;
    message: string;
    data?: unknown;
  };
}

// Payroll API Response Types based on PayrollGrid_API_Response_Documentation.md
export interface PayrollRecord {
  // Owner/Contract Information
  owner_id: number;
  owner_type: number;
  owner_names: string;
  egn_eik: string;
  first_name?: string;
  surname?: string;
  lastname?: string;
  phone?: string;
  mobile?: string;
  iban?: string;
  rep_names?: string;
  rent_place?: string;
  land?: string;
  is_dead: boolean;
  is_heritor: boolean;

  // Area and Land Data
  area: string;
  cultivated_area: string;
  pu_area: string;
  has_personal_use: boolean;
  mestnost?: string;
  category?: string;
  c_type?: string;
  area_type?: string;

  // Contract Data
  farming: number;
  contract_array: number[];
  c_num_array: string[];
  sv_num_array: string[];
  sv_num?: string;
  sv_date?: string;
  plots_array: number[];
  plots_name_array: string[];

  // Financial Data (Money)
  renta: string;
  contract_renta: number;
  charged_renta?: string;
  paid_renta?: string;
  unpaid_renta: string;
  over_paid: string;
  converted_renta_nat?: number;

  // Natura (In-Kind) Rent Data
  renta_nat?: Record<string, unknown>;
  renta_nat_text?: string;
  charged_renta_nat?: Record<string, unknown>;
  charged_renta_nat_text?: string;
  paid_renta_nat?: string;
  paid_renta_nat_details?: Record<string, unknown>;
  unpaid_renta_nat?: string;
  unpaid_renta_nat_arr?: Record<string, unknown>;
  over_paid_nat?: string;
  over_paid_renta_nat_arr?: Record<string, unknown>;
  renta_nat_type?: string;
  unpaid_renta_nat_unit_value?: string;

  // Payment Details
  paid_renta_by?: string;
  paid_renta_by_arr?: Record<string, unknown>;
  paid_renta_nat_by?: string;
  paid_renta_nat_by_arr?: Record<string, unknown>;
  paid_renta_nat_by_detailed?: string;
  total_by_renta?: string;
  total_by_renta_sum?: number;
  total_by_renta_nat?: string;
  total_by_renta_nat_arr?: Record<string, unknown>;

  // Complex Nested Data Structures
  plots_contracts_area_array?: Record<string, unknown>;
  plots_contracts_renta?: Record<string, unknown>;
  plots_contracts_charged_renta?: Record<string, unknown>;
  plots_contracts_renta_nat?: Record<string, unknown>;
  plots_contracts_charged_renta_nat?: Record<string, unknown>;
  owner_plots_percent?: Record<string, unknown>;
  paid_renta_by_contract?: Record<string, unknown>;
  paid_renta_nat_by_contract?: Record<string, unknown>;

  // UI/Display Properties
  id: number;
  iconCls?: string;
  children?: PayrollRecord[];
}

export interface PayrollFooter {
  iconCls?: string;
  owner_names?: string;
  rep_names?: string;
  id?: null;

  // Area Data
  all_owner_area?: string;
  owner_area?: string;
  all_owner_contract_area?: string;
  cultivated_area?: string;
  pu_area?: string;

  // Financial Data (Money)
  renta?: string;
  charged_renta?: string;
  paid_renta?: string;
  unpaid_renta?: string;
  over_paid?: string;
  paid_via_money?: number;
  all_renta_money?: string;
  contract_renta?: string;
  contract_renta_value?: string;
  charged_renta_value?: string;

  // Formatted Financial Text (with Currency Conversion)
  renta_txt?: string;
  charged_renta_txt?: string;
  paid_renta_txt?: string;
  unpaid_renta_txt?: string;
  contract_renta_txt?: string;
  over_paid_txt?: string;
  plot_rent_txt?: string;

  // Payment Details
  paid_renta_by?: string;
  paid_renta_by_txt?: string;
  paid_renta_by_arr?: Record<string, unknown>;

  // Natura (In-Kind) Rent Data
  renta_nat_text?: string;
  charged_renta_nat_text?: string;
  paid_renta_nat?: string;
  paid_renta_nat_by?: string;
  paid_renta_nat_by_detailed?: string;
  unpaid_renta_nat?: string;
  unpaid_renta_nat_unit_value?: string;
  over_paid_nat?: string;
  renta_nat_type?: string;

  // Additional Properties
  plot_rent?: string;
  rent_place_name?: string;
  category?: string;
  osz_num?: string;
  osz_date?: string;
  over_paid_nat_text?: string;
  unpaid_renta_nat_text?: string;

  // Personal Use Data
  personal_use_nat_types_names?: string;
  personal_use_renta?: string;
  personal_use_paid_renta?: string;
  personal_use_unpaid_renta?: string;
  personal_use_treatments_sum?: string;
  personal_use_paid_treatments?: string;
  personal_use_unpaid_treatments?: string;

  // Internal Calculation Fields
  all_owner_no_rounded?: string;
  all_owner_no_rounded_contract?: string;
}

export interface PayrollApiResponse {
  rows: PayrollRecord[];
  total: number;
  footer: PayrollFooter[];
}

// Error Types
export interface McpError {
  code: number;
  message: string;
  data?: unknown;
}

export const MCP_ERROR_CODES = {
  EXTERNAL_API_ERROR: -32001,
  VALIDATION_ERROR: -32002,
  INTERNAL_ERROR: -32603,
  INVALID_REQUEST: -32600,
  METHOD_NOT_FOUND: -32601,
  INVALID_PARAMS: -32602,
} as const;
