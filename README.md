# Payroll MCP Server

A comprehensive Model Context Protocol (MCP) server for payroll data integration with SSE transport.

## Features

- **MCP Protocol Compliance**: Implements SSEServerTransport as specified in MCP 2025-06-18
- **Simple Authentication**: Uses external service access tokens for API authentication
- **Payroll Tool**: Comprehensive payroll data retrieval with extensive filtering options
- **Security**: DNS rebinding protection, rate limiting, and CORS
- **Production Ready**: Docker deployment, structured logging, error handling, and monitoring
- **TypeScript**: Full type safety and modern development experience

## Quick Start

### Prerequisites

- Node.js 18+ 
- Docker and Docker Compose (for containerized deployment)
- Keycloak server (or compatible OIDC provider)
- Access to external payroll API

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd payroll-mcp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Build the application**
   ```bash
   npm run build
   ```

5. **Start the server**
   ```bash
   npm start
   ```

### Docker Deployment

1. **Using Docker Compose (Recommended)**
   ```bash
   # Copy and configure environment
   cp .env.example .env
   
   # Start the MCP server
   docker-compose up -d
   ```

2. **Using Docker directly**
   ```bash
   # Build the image
   docker build -t payroll-mcp-server .
   
   # Run the container
   docker run -p 3000:3000 --env-file .env payroll-mcp-server
   ```

## Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `EXTERNAL_SERVICE_ACCESS_TOKEN` | Access token for external API | `your-api-access-token` |
| `EXTERNAL_API_URL` | Payroll API base URL | `https://api.payroll.example.com` |

### Optional Configuration

See `.env.example` for all available configuration options including:
- Security settings (DNS rebinding protection, CORS, rate limiting)
- API configuration (timeouts, retries)
- Logging and monitoring
- Development/testing options

## API Documentation

### MCP Endpoint

The server provides MCP endpoints using Server-Sent Events (SSE) transport:

- **GET `/mcp`**: Establishes SSE connection for server-to-client communication
- **POST `/mcp`**: Handles client-to-server JSON-RPC requests

### Authentication

The server uses a configured external service access token to authenticate with the payroll API. No client authentication is required for MCP connections.

### Payroll Tool

The server provides a `payroll` tool with the following parameters:

#### Core Parameters (Required)
- `type`: Operation mode (`"owners"`, `"sums"`, `"payroll_by_owner"`)
- `farming_year`: Farming year ID (integer)

#### Optional Filters
- **Date Range**: `payroll_from_date`, `payroll_to_date`
- **Location**: `payroll_ekate`, `payroll_farming`, `rent_place`
- **Owner Filters**: `owner_type`, `owner_names`, `egn`, `eik`, `company_name`
- **Advanced Filters**: `owner_egns`, `company_eiks`, `rep_names`, `rep_egn`
- **Heritor Filters**: `heritor_names`, `heritor_egn`

#### Pagination & Formatting
- `page`: Page number (default: 1)
- `rows`: Records per page (default: 30, max: 1000)
- `sort`: Sort field (default: "owner_names")
- `order`: Sort order ("asc" or "desc")
- `format`: Response format ("full", "summary", "minimal")

#### Example Usage

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "payroll",
    "arguments": {
      "type": "owners",
      "farming_year": 16,
      "payroll_from_date": "2024-10-01",
      "payroll_to_date": "2025-09-30",
      "owner_type": "1",
      "page": 1,
      "rows": 50,
      "format": "summary"
    }
  }
}
```

## Development

### Scripts

- `npm run dev`: Start development server with hot reload
- `npm run build`: Build TypeScript to JavaScript
- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues
- `npm test`: Run tests
- `npm run clean`: Clean build directory

### Project Structure

```
src/
├── server.ts              # Main server application
├── auth/
│   ├── oidc.ts            # OIDC service implementation
│   └── middleware.ts      # Authentication middleware
├── tools/
│   └── payroll.ts         # Payroll tool implementation
├── services/
│   └── payroll-api.ts     # External API client
├── types/
│   └── payroll.ts         # Type definitions
└── utils/
    └── logger.ts          # Logging utilities
```

### Adding New Tools

1. Create tool implementation in `src/tools/`
2. Register tool in `src/server.ts`
3. Add type definitions in `src/types/`
4. Update documentation

## Security

### Authentication & Authorization
- OIDC/OAuth 2.1 compliant authentication
- Token validation with audience claim verification (RFC 9068)
- Support for token introspection and JWT validation

### Security Features
- DNS rebinding protection
- CORS configuration
- Rate limiting
- Helmet security headers
- Input validation and sanitization

### Production Deployment
- Non-root container user
- Health checks
- Graceful shutdown handling
- Structured logging
- Error monitoring

## Monitoring

### Health Checks
- `/health`: Application health status
- Docker health checks included
- Kubernetes readiness/liveness probes supported

### Logging
- Structured JSON logging with Winston
- Request tracing with correlation IDs
- Component-specific log levels
- Log rotation and retention

### Metrics
- Request/response metrics
- Authentication events
- External API call metrics
- Error rates and latencies

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify OIDC configuration
   - Check token expiration
   - Validate audience claims

2. **External API Errors**
   - Check API endpoint availability
   - Verify access token permissions
   - Review API timeout settings

3. **Docker Issues**
   - Ensure all required environment variables are set
   - Check container logs: `docker-compose logs payroll-mcp`
   - Verify network connectivity between services

### Debug Mode

Enable debug logging:
```bash
LOG_LEVEL=debug npm run dev
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details
