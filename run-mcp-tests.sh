#!/bin/bash

# MCP Server Test Runner
# This script sets up and runs comprehensive tests for your MCP server

set -e

echo "🧪 MCP Server Test Suite"
echo "========================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check if the MCP server is running
echo "🔍 Checking if MCP server is running on port 3001..."
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo "❌ MCP server is not running on port 3001"
    echo "Please start your MCP server first:"
    echo "   docker-compose up -d"
    exit 1
fi

echo "✅ MCP server is running"

# Install test dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing test dependencies..."
    cp test-package.json package.json
    npm install
    echo "✅ Dependencies installed"
else
    echo "✅ Dependencies already installed"
fi

# Run the tests
echo ""
echo "🚀 Running MCP Protocol Compliance Tests..."
echo "============================================"

node test-mcp-client.js

echo ""
echo "✨ Test suite completed!"
echo ""
echo "📋 What was tested:"
echo "   ✅ SSE Connection establishment"
echo "   ✅ MCP Protocol initialization handshake"
echo "   ✅ Tools listing and validation"
echo "   ✅ Server capability verification"
echo "   ✅ Protocol compliance checks"
echo "   ✅ Performance and reliability tests"
echo ""
echo "🎉 Your MCP server is working correctly!"
