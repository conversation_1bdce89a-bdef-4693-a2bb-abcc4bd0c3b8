#!/usr/bin/env node

/**
 * Simple test script to validate the MCP server implementation
 * This script tests basic functionality without requiring full OIDC setup
 */

import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

const TEST_ENV = {
  NODE_ENV: 'development',
  PORT: '3001',
  LOG_LEVEL: 'info',
  SKIP_OIDC: 'true',

  // Mock OIDC configuration for testing
  OIDC_ISSUER_URL: 'http://localhost:8080/realms/test',
  OIDC_CLIENT_ID: 'test-client',
  OIDC_CLIENT_SECRET: 'test-secret',
  OIDC_REDIRECT_URI: 'http://localhost:3001/auth/callback',
  OIDC_SCOPES: 'openid profile email',

  // Mock external API configuration
  EXTERNAL_API_URL: 'http://localhost:8081/api',
  API_TIMEOUT: '10000',
  API_RETRIES: '2',

  // Security configuration
  ENABLE_DNS_REBINDING_PROTECTION: 'false',
  ALLOWED_HOSTS: '127.0.0.1,localhost',
  ALLOWED_ORIGINS: 'http://localhost:3001',
  RATE_LIMIT_WINDOW_MS: '60000',
  RATE_LIMIT_MAX: '10',

  MCP_SERVER_URL: 'http://localhost:3001'
};

async function testServerStartup() {
  console.log('🚀 Testing MCP Server startup...');
  
  const server = spawn('node', ['dist/server.js'], {
    env: { ...process.env, ...TEST_ENV },
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let serverOutput = '';
  let serverError = '';

  server.stdout.on('data', (data) => {
    const output = data.toString();
    serverOutput += output;
    console.log('📝 Server output:', output.trim());
  });

  server.stderr.on('data', (data) => {
    const error = data.toString();
    serverError += error;
    console.log('❌ Server error:', error.trim());
  });

  // Wait for server to start
  await setTimeout(5000);

  // Test health endpoint
  try {
    console.log('🔍 Testing health endpoint...');
    const response = await fetch('http://localhost:3001/health');
    
    if (response.ok) {
      const health = await response.json();
      console.log('✅ Health check passed:', health);
    } else {
      console.log('❌ Health check failed:', response.status, response.statusText);
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
  }

  // Test OIDC discovery endpoint
  try {
    console.log('🔍 Testing OIDC discovery endpoint...');
    const response = await fetch('http://localhost:3001/.well-known/oauth-authorization-server');

    if (response.ok) {
      const discovery = await response.json();
      console.log('✅ OIDC discovery passed:', Object.keys(discovery));
    } else {
      console.log('❌ OIDC discovery failed:', response.status, response.statusText);
    }
  } catch (error) {
    console.log('❌ OIDC discovery error:', error.message);
  }

  // Test OAuth 2.0 Protected Resource Metadata proxy endpoint
  try {
    console.log('🔍 Testing OAuth 2.0 protected resource metadata proxy endpoint...');
    const response = await fetch('http://localhost:3001/.well-known/oauth-protected-resource');

    console.log('📊 OAuth 2.0 protected resource response status:', response.status);
    const responseText = await response.text();
    console.log('📊 OAuth 2.0 protected resource response preview:', responseText.substring(0, 200) + '...');

    if (response.status === 503) {
      console.log('✅ OAuth 2.0 protected resource endpoint correctly handles unreachable Keycloak (expected in test environment)');
    } else if (response.status === 500) {
      console.log('✅ OAuth 2.0 protected resource endpoint correctly handles configuration errors (expected in test environment)');
    } else if (response.ok) {
      console.log('✅ OAuth 2.0 protected resource endpoint successfully proxied to Keycloak');
    } else {
      console.log('⚠️  OAuth 2.0 protected resource endpoint response:', response.status);
    }
  } catch (error) {
    console.log('❌ OAuth 2.0 protected resource endpoint error:', error.message);
  }

  // Test MCP endpoint (should fail without auth, but server should respond)
  try {
    console.log('🔍 Testing MCP endpoint...');
    const response = await fetch('http://localhost:3001/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'MCP-Protocol-Version': '2025-06-18'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2025-06-18',
          capabilities: {},
          clientInfo: {
            name: 'test-client',
            version: '1.0.0'
          }
        }
      })
    });
    
    console.log('📊 MCP endpoint response status:', response.status);
    const responseText = await response.text();
    console.log('📊 MCP endpoint response:', responseText.substring(0, 200) + '...');
    
    if (response.status === 401) {
      console.log('✅ MCP endpoint correctly requires authentication');
    } else {
      console.log('⚠️  MCP endpoint response:', response.status);
    }
  } catch (error) {
    console.log('❌ MCP endpoint error:', error.message);
  }

  // Clean up
  console.log('🧹 Cleaning up...');
  server.kill('SIGTERM');
  
  await setTimeout(2000);
  
  if (!server.killed) {
    console.log('🔪 Force killing server...');
    server.kill('SIGKILL');
  }

  console.log('✅ Test completed');
  
  // Check if server started successfully
  if (serverOutput.includes('Payroll MCP Server started')) {
    console.log('🎉 Server startup test PASSED');
    return true;
  } else {
    console.log('💥 Server startup test FAILED');
    console.log('Server output:', serverOutput);
    console.log('Server errors:', serverError);
    return false;
  }
}

async function testBuild() {
  console.log('🔨 Testing build...');
  
  const build = spawn('npm', ['run', 'build'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let buildOutput = '';
  let buildError = '';

  build.stdout.on('data', (data) => {
    buildOutput += data.toString();
  });

  build.stderr.on('data', (data) => {
    buildError += data.toString();
  });

  return new Promise((resolve) => {
    build.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Build test PASSED');
        resolve(true);
      } else {
        console.log('❌ Build test FAILED');
        console.log('Build output:', buildOutput);
        console.log('Build errors:', buildError);
        resolve(false);
      }
    });
  });
}

async function runTests() {
  console.log('🧪 Starting MCP Server Tests\n');
  
  const buildSuccess = await testBuild();
  if (!buildSuccess) {
    console.log('💥 Build failed, skipping server tests');
    process.exit(1);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  const serverSuccess = await testServerStartup();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Results:');
  console.log('  Build:', buildSuccess ? '✅ PASSED' : '❌ FAILED');
  console.log('  Server:', serverSuccess ? '✅ PASSED' : '❌ FAILED');
  
  if (buildSuccess && serverSuccess) {
    console.log('\n🎉 All tests PASSED! The MCP server is working correctly.');
    process.exit(0);
  } else {
    console.log('\n💥 Some tests FAILED. Please check the output above.');
    process.exit(1);
  }
}

// Run tests
runTests().catch((error) => {
  console.error('💥 Test runner error:', error);
  process.exit(1);
});
