{"name": "payroll-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for payroll data with OIDC authentication", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsx watch src/server.ts", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest", "docker:build": "docker build -t payroll-mcp-server .", "docker:run": "docker run -p 3000:3000 --env-file .env payroll-mcp-server", "docker:compose": "docker-compose up -d"}, "keywords": ["mcp", "model-context-protocol", "payroll", "oidc", "keycloak", "typescript"], "author": "Payroll MCP Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.12.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "winston": "^3.11.0", "zod": "^3.22.4", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}