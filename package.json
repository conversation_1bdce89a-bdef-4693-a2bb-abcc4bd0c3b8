{"name": "mcp-server-test-client", "version": "1.0.0", "description": "Test client for validating MCP server compliance", "type": "module", "main": "test-mcp-client.js", "scripts": {"test": "node test-mcp-client.js", "test:verbose": "DEBUG=* node test-mcp-client.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.18.1", "eventsource": "^2.0.2", "node-fetch": "^3.3.2"}, "keywords": ["mcp", "model-context-protocol", "test", "client", "validation"], "author": "MCP Test Suite", "license": "MIT"}