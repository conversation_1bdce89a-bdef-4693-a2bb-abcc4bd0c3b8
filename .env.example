# =============================================================================
# Payroll MCP Server Environment Configuration
# =============================================================================
# Copy this file to .env and update the values according to your environment
# All variables marked as REQUIRED must be set for the server to start

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Server port (default: 3000)
PORT=3000

# Node.js environment (development, production, test)
NODE_ENV=production

# Logging level (error, warn, info, debug)
LOG_LEVEL=info

# =============================================================================
# EXTERNAL SERVICE AUTHENTICATION (REQUIRED)
# =============================================================================

# External service access token (REQUIRED)
# This token will be used to authenticate with the external payroll API
EXTERNAL_SERVICE_ACCESS_TOKEN=your-external-service-access-token-here

# =============================================================================
# EXTERNAL PAYROLL API CONFIGURATION (REQUIRED)
# =============================================================================

# External payroll API base URL (REQUIRED)
# Example: https://api.payroll-system.com
EXTERNAL_API_URL=http://127.0.0.1:8067

# API request timeout in milliseconds (default: 30000)
API_TIMEOUT=30000

# Number of retry attempts for failed API calls (default: 3)
API_RETRIES=3

# Delay between retry attempts in milliseconds (default: 1000)
API_RETRY_DELAY=1000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Enable DNS rebinding protection (recommended: true)
ENABLE_DNS_REBINDING_PROTECTION=true

# Allowed hosts for DNS rebinding protection (comma-separated)
# Include localhost for development, your domain for production
ALLOWED_HOSTS=127.0.0.1,localhost,your-domain.com

# Allowed origins for CORS (comma-separated)
# Leave empty to allow all origins (not recommended for production)
ALLOWED_ORIGINS=https://your-frontend.com,https://your-other-domain.com

# Rate limiting window in milliseconds (default: 900000 = 15 minutes)
RATE_LIMIT_WINDOW_MS=900000

# Maximum requests per rate limit window (default: 100)
RATE_LIMIT_MAX=100

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================

# Enable verbose logging for debugging
DEBUG_MODE=false

# Mock external API responses for testing
MOCK_EXTERNAL_API=false

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Enable metrics collection
ENABLE_METRICS=true

# Metrics port (if different from main port)
METRICS_PORT=9090

# Enable distributed tracing
ENABLE_TRACING=false

# Tracing endpoint (e.g., Jaeger)
TRACING_ENDPOINT=http://jaeger:14268/api/traces

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example for local development:
# EXTERNAL_SERVICE_ACCESS_TOKEN=your-dev-token-here
# EXTERNAL_API_URL=http://localhost:8081
# ALLOWED_HOSTS=127.0.0.1,localhost
# ALLOWED_ORIGINS=http://localhost:3000

# Example for production deployment:
# EXTERNAL_SERVICE_ACCESS_TOKEN=your-production-token-here
# EXTERNAL_API_URL=https://api.payroll.yourcompany.com
# ALLOWED_HOSTS=mcp.yourcompany.com
# ALLOWED_ORIGINS=https://app.yourcompany.com
# ENABLE_DNS_REBINDING_PROTECTION=true
# LOG_LEVEL=warn
