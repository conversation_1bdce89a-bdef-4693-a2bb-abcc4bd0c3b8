# MCP Server Test Suite

This test suite validates that your MCP server properly implements the **Model Context Protocol (MCP)** specification. It performs comprehensive testing of the SSE transport, protocol handshake, and server functionality.

## 🎯 What This Test Suite Validates

### ✅ **Core Protocol Compliance**
- **SSE Connection**: Establishes Server-Sent Events connection to `/mcp` endpoint
- **MCP Initialization**: Performs proper protocol handshake with `initialize` and `initialized` messages
- **Session Management**: Validates session ID handling and message routing
- **JSON-RPC Compliance**: Ensures proper JSON-RPC 2.0 message format

### ✅ **Server Functionality**
- **Tools Discovery**: Lists and validates available tools (expects "payroll" tool)
- **Capability Negotiation**: Verifies server capabilities and feature support
- **Error Handling**: Tests server response to invalid requests and malformed data
- **Health Monitoring**: Validates health endpoint functionality

### ✅ **Performance & Reliability**
- **Concurrent Requests**: Tests handling of multiple simultaneous requests
- **Connection Stability**: Monitors SSE connection reliability
- **Response Times**: Measures server response performance
- **Protocol Compliance**: Validates adherence to MCP specification

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- Your MCP server running on `http://localhost:3001`

### Run Tests
```bash
# Make the script executable and run
./run-mcp-tests.sh
```

Or manually:
```bash
# Install dependencies
cp test-package.json package.json
npm install

# Run the test client
node test-mcp-client.js
```

## 📊 Test Output Example

```
🧪 Starting MCP Server Compliance Tests

Target Server: http://localhost:3001/mcp
==================================================
🔌 Test 1: Establishing SSE Connection...
✅ SSE connection established successfully
📡 Received endpoint event: /mcp?sessionId=74f34b46-0e8d-4d6e-a4f4-0bc68451ebb9
🆔 Session ID extracted: 74f34b46-0e8d-4d6e-a4f4-0bc68451ebb9

🚀 Test 2: Sending MCP Initialize Request...
✅ Initialize response received: {
  "protocolVersion": "2024-11-05",
  "serverInfo": {
    "name": "payroll-mcp-server",
    "version": "1.0.0"
  },
  "capabilities": {
    "tools": {
      "listChanged": true
    }
  }
}
📢 Sending initialized notification...
✅ Initialized notification accepted

🛠️  Test 3: Listing Available Tools...
✅ Tools list received: {
  "tools": [
    {
      "name": "payroll",
      "description": "Retrieve payroll data for owners, heritors, and their associated contracts with comprehensive filtering options",
      "inputSchema": { ... }
    }
  ]
}
✅ Payroll tool found

🔍 Test 4: Verifying Server Functionality...
✅ Health endpoint working: {
  "status": "healthy",
  "timestamp": "2025-09-19T15:41:19.580Z",
  "version": "1.0.0",
  "services": {
    "external_api": "configured"
  }
}
✅ Server supports tools
✅ Server supports tool list change notifications

📋 Test 5: Protocol Compliance Validation...
✅ Server properly rejects invalid methods
✅ Server properly handles malformed JSON
✅ All protocol compliance checks passed

⚡ Test 6: Performance and Reliability...
✅ Handled 5 concurrent requests in 234ms
✅ SSE connection remains stable

📊 TEST REPORT
==================================================
🔧 Server Information:
   Name: payroll-mcp-server
   Version: 1.0.0
   Protocol: 2024-11-05

🛠️  Tools Available:
   - payroll: Retrieve payroll data for owners, heritors, and their associated contracts with comprehensive filtering options

✅ Compliance Status:
   SSE Connection: ✅ Working
   MCP Initialization: ✅ Working
   Tools Listing: ✅ Working
   Protocol Compliance: ✅ Passed

🔧 Server Capabilities:
   - tools: ✅ Supported

==================================================
🎉 All tests completed successfully!
✅ Your MCP server is properly implementing the protocol
```

## 🔧 Troubleshooting

### Common Issues

**❌ "SSE connection timeout"**
- Ensure your MCP server is running on port 3001
- Check that the `/mcp` endpoint accepts GET requests
- Verify the server returns `Content-Type: text/event-stream`

**❌ "Initialize request failed"**
- Confirm the server accepts POST requests to `/mcp`
- Check server logs for initialization errors
- Verify JSON-RPC 2.0 message format compliance

**❌ "No transport found for sessionId"**
- Ensure proper session management in your server
- Check that the `Mcp-Session-Id` header is being processed
- Verify SSE connection remains open during POST requests

**❌ "Payroll tool not found"**
- Confirm your server registers the "payroll" tool
- Check tool registration happens before client connection
- Verify tools are returned in the `tools/list` response

### Debug Mode
For detailed debugging information:
```bash
DEBUG=* node test-mcp-client.js
```

## 📚 MCP Specification References

This test suite validates compliance with:
- [Model Context Protocol Specification](https://spec.modelcontextprotocol.io/)
- [MCP Transport Layer](https://spec.modelcontextprotocol.io/specification/basic/transports/)
- [MCP Tools Protocol](https://spec.modelcontextprotocol.io/specification/server/tools/)
- [JSON-RPC 2.0 Specification](https://www.jsonrpc.org/specification)

## 🤝 Contributing

To extend the test suite:
1. Add new test methods to the `MCPTestClient` class
2. Update the `runAllTests()` method to include new tests
3. Add validation for additional MCP features (resources, prompts, sampling)

## 📄 License

MIT License - Feel free to use and modify for your MCP server testing needs.
