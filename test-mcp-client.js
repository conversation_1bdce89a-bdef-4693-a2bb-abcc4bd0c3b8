#!/usr/bin/env node

/**
 * MCP Server Test Client
 * 
 * This script validates that the MCP server at http://localhost:3001/mcp
 * properly implements the Model Context Protocol specification.
 * 
 * Tests performed:
 * 1. SSE Connection Establishment
 * 2. MCP Protocol Initialization
 * 3. Tools Listing
 * 4. Server Capability Verification
 * 5. Protocol Compliance Validation
 */

import EventSource from 'eventsource';
import fetch from 'node-fetch';

class MCPTestClient {
  constructor(serverUrl = 'http://localhost:3001/mcp') {
    this.serverUrl = serverUrl;
    this.sessionId = null;
    this.eventSource = null;
    this.messageId = 1;
    this.pendingRequests = new Map();
    this.serverCapabilities = null;
    this.isInitialized = false;
  }

  /**
   * Generate unique message ID for JSON-RPC requests
   */
  getNextMessageId() {
    return this.messageId++;
  }

  /**
   * Test 1: Establish SSE Connection
   */
  async testSSEConnection() {
    console.log('🔌 Test 1: Establishing SSE Connection...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('SSE connection timeout after 10 seconds'));
      }, 10000);

      this.eventSource = new EventSource(this.serverUrl, {
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      this.eventSource.onopen = () => {
        clearTimeout(timeout);
        console.log('✅ SSE connection established successfully');
        resolve();
      };

      this.eventSource.onerror = (error) => {
        clearTimeout(timeout);
        console.error('❌ SSE connection failed:', error);
        reject(error);
      };

      // Listen for endpoint event (MCP specification requirement)
      this.eventSource.addEventListener('endpoint', (event) => {
        console.log('📡 Received endpoint event:', event.data);
        const endpointUrl = event.data;
        if (endpointUrl.includes('sessionId=')) {
          this.sessionId = endpointUrl.split('sessionId=')[1];
          console.log('🆔 Session ID extracted:', this.sessionId);
        }
      });

      // Listen for message events
      this.eventSource.addEventListener('message', (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleServerMessage(message);
        } catch (error) {
          console.error('❌ Failed to parse server message:', error);
        }
      });
    });
  }

  /**
   * Handle incoming messages from server
   */
  handleServerMessage(message) {
    console.log('📨 Received server message:', JSON.stringify(message, null, 2));
    
    if (message.id && this.pendingRequests.has(message.id)) {
      const { resolve, reject } = this.pendingRequests.get(message.id);
      this.pendingRequests.delete(message.id);
      
      if (message.error) {
        reject(new Error(`Server error: ${message.error.message} (code: ${message.error.code})`));
      } else {
        resolve(message.result);
      }
    }
  }

  /**
   * Send JSON-RPC message to server via POST
   */
  async sendMessage(method, params = {}) {
    const messageId = this.getNextMessageId();
    const message = {
      jsonrpc: '2.0',
      id: messageId,
      method,
      params
    };

    console.log('📤 Sending message:', JSON.stringify(message, null, 2));

    const headers = {
      'Content-Type': 'application/json',
    };

    // Add session ID header if available
    if (this.sessionId) {
      headers['Mcp-Session-Id'] = this.sessionId;
    }

    // Set up promise to wait for SSE response
    const responsePromise = new Promise((resolve, reject) => {
      this.pendingRequests.set(messageId, { resolve, reject });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.pendingRequests.has(messageId)) {
          this.pendingRequests.delete(messageId);
          reject(new Error(`Request timeout for message ID ${messageId}`));
        }
      }, 30000);
    });

    // Send the POST request
    const response = await fetch(this.serverUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(message)
    });

    // Check if the POST was accepted
    if (!response.ok) {
      this.pendingRequests.delete(messageId);

      // Try to parse error response
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorBody = await response.text();
        if (errorBody && errorBody !== 'Accepted') {
          const errorJson = JSON.parse(errorBody);
          if (errorJson.error) {
            errorMessage = `Server error: ${errorJson.error.message} (code: ${errorJson.error.code})`;
          }
        }
      } catch (e) {
        // Keep the original error message
      }

      throw new Error(errorMessage);
    }

    // For SSE transport, the response comes via SSE, not HTTP response body
    // Wait for the SSE response
    return responsePromise;
  }

  /**
   * Test 2: Send Initialize Request
   */
  async testInitialization() {
    console.log('\n🚀 Test 2: Sending MCP Initialize Request...');
    
    const initParams = {
      protocolVersion: '2024-11-05',
      capabilities: {
        roots: {
          listChanged: false
        },
        sampling: {}
      },
      clientInfo: {
        name: 'mcp-test-client',
        version: '1.0.0'
      }
    };

    try {
      const result = await this.sendMessage('initialize', initParams);
      console.log('✅ Initialize response received:', JSON.stringify(result, null, 2));
      
      // Validate response structure
      if (!result.protocolVersion) {
        throw new Error('Missing protocolVersion in initialize response');
      }
      
      if (!result.serverInfo) {
        throw new Error('Missing serverInfo in initialize response');
      }

      this.serverCapabilities = result.capabilities || {};
      console.log('🔧 Server capabilities:', JSON.stringify(this.serverCapabilities, null, 2));
      
      // Send initialized notification
      await this.sendInitializedNotification();
      
      return result;
    } catch (error) {
      console.error('❌ Initialize request failed:', error.message);
      throw error;
    }
  }

  /**
   * Send initialized notification (required by MCP spec)
   */
  async sendInitializedNotification() {
    console.log('📢 Sending initialized notification...');

    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.sessionId) {
      headers['Mcp-Session-Id'] = this.sessionId;
    }

    const notification = {
      jsonrpc: '2.0',
      method: 'notifications/initialized'
    };

    const response = await fetch(this.serverUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(notification)
    });

    // Accept both 200 and 202 status codes for notifications
    if (response.status === 200 || response.status === 202) {
      console.log('✅ Initialized notification accepted');
      this.isInitialized = true;
    } else {
      // Try to get error details
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorBody = await response.text();
        if (errorBody) {
          const errorJson = JSON.parse(errorBody);
          if (errorJson.error) {
            errorMessage = `Server error: ${errorJson.error.message} (code: ${errorJson.error.code})`;
          }
        }
      } catch (e) {
        // Keep the original error message
      }
      throw new Error(`Initialized notification failed: ${errorMessage}`);
    }
  }

  /**
   * Test 3: List Available Tools
   */
  async testToolsListing() {
    console.log('\n🛠️  Test 3: Listing Available Tools...');
    
    if (!this.isInitialized) {
      throw new Error('Client must be initialized before listing tools');
    }

    try {
      const result = await this.sendMessage('tools/list');
      console.log('✅ Tools list received:', JSON.stringify(result, null, 2));
      
      // Validate tools structure
      if (!Array.isArray(result.tools)) {
        throw new Error('Tools list should be an array');
      }

      // Check for payroll tool specifically
      const payrollTool = result.tools.find(tool => tool.name === 'payroll');
      if (!payrollTool) {
        console.warn('⚠️  Payroll tool not found in tools list');
      } else {
        console.log('✅ Payroll tool found:', JSON.stringify(payrollTool, null, 2));
        
        // Validate tool structure
        if (!payrollTool.description) {
          console.warn('⚠️  Payroll tool missing description');
        }
        if (!payrollTool.inputSchema) {
          console.warn('⚠️  Payroll tool missing inputSchema');
        }
      }

      return result;
    } catch (error) {
      console.error('❌ Tools listing failed:', error.message);
      throw error;
    }
  }

  /**
   * Test 4: Verify Server Functionality
   */
  async testServerFunctionality() {
    console.log('\n🔍 Test 4: Verifying Server Functionality...');
    
    // Test health endpoint
    try {
      const healthResponse = await fetch('http://localhost:3001/health');
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        console.log('✅ Health endpoint working:', JSON.stringify(healthData, null, 2));
      } else {
        console.warn('⚠️  Health endpoint returned:', healthResponse.status);
      }
    } catch (error) {
      console.warn('⚠️  Health endpoint test failed:', error.message);
    }

    // Verify server capabilities
    if (this.serverCapabilities) {
      console.log('🔧 Analyzing server capabilities...');
      
      if (this.serverCapabilities.tools) {
        console.log('✅ Server supports tools');
        if (this.serverCapabilities.tools.listChanged) {
          console.log('✅ Server supports tool list change notifications');
        }
      } else {
        console.warn('⚠️  Server does not declare tools capability');
      }
    }
  }

  /**
   * Clean up resources
   */
  cleanup() {
    if (this.eventSource) {
      this.eventSource.close();
      console.log('🧹 SSE connection closed');
    }
  }

  /**
   * Test 5: Protocol Compliance Validation
   */
  async testProtocolCompliance() {
    console.log('\n📋 Test 5: Protocol Compliance Validation...');

    const issues = [];

    // Check if server properly handles invalid requests
    try {
      console.log('🔍 Testing invalid method handling...');
      await this.sendMessage('invalid/method');
      issues.push('Server should reject invalid methods');
    } catch (error) {
      if (error.message.includes('Method not found') || error.message.includes('-32601')) {
        console.log('✅ Server properly rejects invalid methods');
      } else {
        issues.push(`Unexpected error for invalid method: ${error.message}`);
      }
    }

    // Test malformed JSON handling
    try {
      console.log('🔍 Testing malformed JSON handling...');
      const headers = { 'Content-Type': 'application/json' };
      if (this.sessionId) headers['Mcp-Session-Id'] = this.sessionId;

      const response = await fetch(this.serverUrl, {
        method: 'POST',
        headers,
        body: '{"invalid": json}'
      });

      if (response.status >= 400) {
        console.log('✅ Server properly handles malformed JSON');
      } else {
        issues.push('Server should reject malformed JSON');
      }
    } catch (error) {
      console.log('✅ Server properly handles malformed JSON');
    }

    // Report compliance issues
    if (issues.length > 0) {
      console.log('⚠️  Protocol compliance issues found:');
      issues.forEach(issue => console.log(`   - ${issue}`));
    } else {
      console.log('✅ All protocol compliance checks passed');
    }

    return issues;
  }

  /**
   * Test 6: Performance and Reliability
   */
  async testPerformanceReliability() {
    console.log('\n⚡ Test 6: Performance and Reliability...');

    // Test multiple rapid requests
    console.log('🔍 Testing rapid request handling...');
    const startTime = Date.now();
    const promises = [];

    for (let i = 0; i < 5; i++) {
      promises.push(this.sendMessage('tools/list'));
    }

    try {
      await Promise.all(promises);
      const duration = Date.now() - startTime;
      console.log(`✅ Handled 5 concurrent requests in ${duration}ms`);
    } catch (error) {
      console.warn('⚠️  Concurrent request handling failed:', error.message);
    }

    // Test connection stability
    console.log('🔍 Testing connection stability...');
    if (this.eventSource && this.eventSource.readyState === EventSource.OPEN) {
      console.log('✅ SSE connection remains stable');
    } else {
      console.warn('⚠️  SSE connection unstable');
    }
  }

  /**
   * Generate comprehensive test report
   */
  generateTestReport(results) {
    console.log('\n📊 TEST REPORT');
    console.log('=' .repeat(50));

    const { initialization, tools, compliance, performance } = results;

    console.log('🔧 Server Information:');
    if (initialization) {
      console.log(`   Name: ${initialization.serverInfo?.name || 'Unknown'}`);
      console.log(`   Version: ${initialization.serverInfo?.version || 'Unknown'}`);
      console.log(`   Protocol: ${initialization.protocolVersion || 'Unknown'}`);
    }

    console.log('\n🛠️  Tools Available:');
    if (tools && tools.tools) {
      tools.tools.forEach(tool => {
        console.log(`   - ${tool.name}: ${tool.description || 'No description'}`);
      });
    }

    console.log('\n✅ Compliance Status:');
    console.log(`   SSE Connection: ✅ Working`);
    console.log(`   MCP Initialization: ✅ Working`);
    console.log(`   Tools Listing: ✅ Working`);
    console.log(`   Protocol Compliance: ${compliance.length === 0 ? '✅ Passed' : '⚠️  Issues found'}`);

    if (this.serverCapabilities) {
      console.log('\n🔧 Server Capabilities:');
      Object.keys(this.serverCapabilities).forEach(cap => {
        console.log(`   - ${cap}: ✅ Supported`);
      });
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting MCP Server Compliance Tests\n');
    console.log('Target Server:', this.serverUrl);
    console.log('=' .repeat(50));

    const results = {};

    try {
      // Test 1: SSE Connection
      await this.testSSEConnection();

      // Wait a moment for connection to stabilize
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 2: Initialize
      results.initialization = await this.testInitialization();

      // Test 3: List Tools
      results.tools = await this.testToolsListing();

      // Test 4: Server Functionality
      await this.testServerFunctionality();

      // Test 5: Protocol Compliance
      results.compliance = await this.testProtocolCompliance();

      // Test 6: Performance
      results.performance = await this.testPerformanceReliability();

      // Generate comprehensive report
      this.generateTestReport(results);

      console.log('\n' + '=' .repeat(50));
      console.log('🎉 All tests completed successfully!');
      console.log('✅ Your MCP server is properly implementing the protocol');

    } catch (error) {
      console.log('\n' + '=' .repeat(50));
      console.error('❌ Test suite failed:', error.message);
      console.error('🔧 Please check your MCP server implementation');
      process.exit(1);
    } finally {
      this.cleanup();
    }
  }
}

// Run the tests
const client = new MCPTestClient();
client.runAllTests().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
