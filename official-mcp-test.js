#!/usr/bin/env node

/**
 * Official MCP SDK Test
 * 
 * This test uses the official MCP TypeScript SDK to connect to your server
 * and validate that it's working correctly.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

class OfficialMCPTest {
  constructor(serverUrl = 'http://localhost:3001/mcp') {
    this.serverUrl = serverUrl;
    this.client = null;
    this.transport = null;
  }

  async run() {
    console.log('🧪 Official MCP SDK Test');
    console.log('========================');
    console.log('Target:', this.serverUrl);
    console.log('');

    try {
      // Step 1: Create client and transport
      await this.createClient();
      
      // Step 2: Connect to server
      await this.connectToServer();
      
      // Step 3: List tools
      await this.testToolsList();
      
      // Step 4: Test server info
      await this.testServerInfo();
      
      console.log('\n✅ All tests passed! Your MCP server is working correctly.');
      console.log('🎉 The server properly implements the MCP protocol.');
      
    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      console.error('Stack:', error.stack);
      process.exit(1);
    } finally {
      if (this.client) {
        await this.client.close();
      }
    }
  }

  async createClient() {
    console.log('🔧 Creating MCP client...');
    
    this.client = new Client(
      {
        name: 'official-mcp-test',
        version: '1.0.0'
      },
      {
        capabilities: {
          roots: {
            listChanged: false
          },
          sampling: {}
        }
      }
    );

    this.transport = new SSEClientTransport(new URL(this.serverUrl));
    
    console.log('✅ Client and transport created');
  }

  async connectToServer() {
    console.log('🔌 Connecting to MCP server...');
    
    await this.client.connect(this.transport);
    
    console.log('✅ Connected to MCP server');
  }

  async testToolsList() {
    console.log('\n🛠️  Testing tools/list...');
    
    const result = await this.client.request(
      { method: 'tools/list' },
      { tools: [] } // Schema for validation
    );

    console.log('✅ Tools list successful');
    const tools = result.tools || [];
    console.log(`   Found ${tools.length} tool(s):`);
    
    tools.forEach(tool => {
      console.log(`   - ${tool.name}: ${tool.description || 'No description'}`);
      if (tool.inputSchema) {
        console.log(`     Input schema: ${JSON.stringify(tool.inputSchema).substring(0, 100)}...`);
      }
    });

    // Check for payroll tool
    const payrollTool = tools.find(tool => tool.name === 'payroll');
    if (payrollTool) {
      console.log('✅ Payroll tool found and registered correctly');
      
      // Validate payroll tool structure
      if (payrollTool.description) {
        console.log('✅ Payroll tool has description');
      } else {
        console.warn('⚠️  Payroll tool missing description');
      }
      
      if (payrollTool.inputSchema) {
        console.log('✅ Payroll tool has input schema');
      } else {
        console.warn('⚠️  Payroll tool missing input schema');
      }
    } else {
      console.warn('⚠️  Payroll tool not found');
    }

    return tools;
  }

  async testServerInfo() {
    console.log('\n📋 Testing server information...');
    
    // The server info should be available from the initialization
    const serverInfo = this.client.getServerVersion();
    if (serverInfo) {
      console.log('✅ Server info available:');
      console.log(`   Name: ${serverInfo.name || 'Unknown'}`);
      console.log(`   Version: ${serverInfo.version || 'Unknown'}`);
    } else {
      console.warn('⚠️  Server info not available');
    }

    // Test server capabilities
    const capabilities = this.client.getServerCapabilities();
    if (capabilities) {
      console.log('✅ Server capabilities:');
      Object.keys(capabilities).forEach(cap => {
        console.log(`   - ${cap}: ✅ Supported`);
      });
    } else {
      console.warn('⚠️  Server capabilities not available');
    }
  }
}

// Check if MCP SDK is available
try {
  // Run the test
  const test = new OfficialMCPTest();
  test.run().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
} catch (error) {
  console.error('❌ Failed to import MCP SDK. Installing...');
  console.log('Please run: npm install @modelcontextprotocol/sdk');
  process.exit(1);
}
